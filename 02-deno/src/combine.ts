import { join } from '@std/path/mod.ts';
import { Config } from './config.ts';
import { ExcelService } from './services/excel.ts';
import { ExtractUtility } from './extract.ts';
import { AnalyzeUtility } from './analyze.ts';
import { logger } from './utils/logger.ts';
import { FileUtils } from './utils/file.ts';
import type {
  CombinedResult,
  ExtractionResult,
  BusinessListing,
  ExcelExportOptions,
  CategoryMapping
} from './types.ts';

export class CombineUtility {
  private config: Config;
  private excelService: ExcelService;
  private extractUtility: ExtractUtility;
  private analyzeUtility: AnalyzeUtility;

  constructor() {
    this.config = {} as Config;
    this.excelService = {} as ExcelService;
    this.extractUtility = new ExtractUtility();
    this.analyzeUtility = new AnalyzeUtility();
  }

  async init(): Promise<void> {
    await logger.init();
    await logger.info('Initializing Combine Utility');

    // Load configuration
    this.config = await Config.getInstance();
    await this.config.validateConfig();

    const processingConfig = this.config.getProcessingConfig();

    // Initialize services
    this.excelService = new ExcelService(processingConfig.outputDir);
    await this.excelService.init();
    await this.extractUtility.init();
    await this.analyzeUtility.init();

    await logger.success('Combine Utility initialized successfully');
  }

  async combine(): Promise<CombinedResult> {
    try {
      await logger.info('Starting data combination process');

      // Load analysis result
      const analysis = await this.analyzeUtility.getLatestAnalysis();
      if (!analysis) {
        throw new Error('No analysis result found. Please run analysis first.');
      }

      // Load all extraction results
      const extractionResults = await this.extractUtility.getAllExtractionResults();
      if (extractionResults.length === 0) {
        throw new Error('No extraction results found. Please run extraction first.');
      }

      await logger.info(`Combining data from ${extractionResults.length} pages`);

      // Combine all listings
      const allListings = this.combineListings(extractionResults);

      // Handle multi-page entries
      const processedListings = this.mergeMultiPageEntries(allListings);

      // Apply categories to listings
      const listingsWithCategories = await this.applyCategoriesFromMapping(processedListings);

      // Calculate statistics
      const statistics = this.calculateStatistics(listingsWithCategories, extractionResults);

      // Create combined result
      const combinedResult: CombinedResult = {
        timestamp: new Date().toISOString(),
        totalListings: listingsWithCategories.length,
        totalPages: extractionResults.length,
        columns: analysis.columns,
        listings: listingsWithCategories,
        statistics,
      };

      // Save combined result
      await this.saveCombinedResult(combinedResult);

      await logger.success(`Data combination completed. Combined ${listingsWithCategories.length} listings from ${extractionResults.length} pages`);

      return combinedResult;

    } catch (error) {
      await logger.error('Data combination failed', error);
      throw error;
    }
  }

  private combineListings(extractionResults: ExtractionResult[]): BusinessListing[] {
    const allListings: BusinessListing[] = [];
    let globalEntryNumber = 1;

    for (const result of extractionResults) {
      for (const listing of result.listings) {
        // Assign global entry number
        listing.entryNumber = globalEntryNumber++;
        allListings.push(listing);
      }
    }

    return allListings;
  }

  private mergeMultiPageEntries(listings: BusinessListing[]): BusinessListing[] {
    const processedListings: BusinessListing[] = [];
    const mergedEntries = new Set<number>();

    for (let i = 0; i < listings.length; i++) {
      if (mergedEntries.has(i)) continue;

      const currentListing = listings[i];

      if (currentListing) {
        // Check if this entry is a continuation from previous page
        if (currentListing.isContinuationFromPreviousPage) {
          // Find the previous page's last entry and merge with it
          const mergedListing = this.mergeWithPreviousPageEntry(listings, i, mergedEntries);
          if (mergedListing) {
            processedListings.push(mergedListing);
          }
        }
        // Check if this entry spans multiple pages (starts here and continues)
        else if (currentListing.spanMultiplePages || currentListing.continuesOnNextPage) {
          const mergedListing = this.findAndMergeRelatedEntries(listings, i, mergedEntries);
          processedListings.push(mergedListing);
        }
        // Regular entry
        else {
          processedListings.push(currentListing);
        }
      }

      mergedEntries.add(i);
    }

    return processedListings;
  }

  private mergeWithPreviousPageEntry(
    listings: BusinessListing[],
    continuationIndex: number,
    mergedEntries: Set<number>
  ): BusinessListing | null {
    const continuationListing = listings[continuationIndex];
    if (!continuationListing) {
      return null;
    }

    // Find the previous page's last entry that should continue to this page
    const previousPageNumber = continuationListing.pageNumber - 1;
    let previousPageLastEntry: BusinessListing | null = null;
    let previousEntryIndex = -1;

    // Look backwards from the continuation entry to find the last entry of the previous page
    // that is marked as continuing to the next page
    for (let i = continuationIndex - 1; i >= 0; i--) {
      const listing = listings[i];
      if (listing && listing.pageNumber === previousPageNumber) {
        // Check if this is the last entry on the previous page that continues to next page
        if (listing.continuesOnNextPage || listing.spanMultiplePages || listing.continuesOnPage === continuationListing.pageNumber) {
          if (!previousPageLastEntry || listing.entryNumber > previousPageLastEntry.entryNumber) {
            previousPageLastEntry = listing;
            previousEntryIndex = i;
          }
        }
      }
    }

    if (!previousPageLastEntry || previousEntryIndex === -1) {
      // No previous page entry found that continues to this page, treat as regular entry
      return continuationListing;
    }

    // Mark both entries as merged
    mergedEntries.add(previousEntryIndex);

    // Create a simple merge of just these two entries
    const mergedListing: BusinessListing = {
      ...previousPageLastEntry,
      data: { ...previousPageLastEntry.data }
    };

    // Merge data fields from continuation, only filling in missing fields
    for (const [key, value] of Object.entries(continuationListing.data)) {
      if (value && value !== '' && value !== null &&
        (!mergedListing.data[key] || mergedListing.data[key] === '' || mergedListing.data[key] === null)) {
        mergedListing.data[key] = value;
      }
    }

    // Concatenate raw text
    const rawTexts: string[] = [];
    if (previousPageLastEntry.rawText) {
      rawTexts.push(previousPageLastEntry.rawText);
    }
    if (continuationListing.rawText) {
      rawTexts.push(continuationListing.rawText);
    }
    if (rawTexts.length > 0) {
      mergedListing.rawText = rawTexts.join(' ');
    }

    // Update confidence to average
    mergedListing.confidence = (previousPageLastEntry.confidence + continuationListing.confidence) / 2;

    // Mark as spanning multiple pages
    mergedListing.spanMultiplePages = true;
    mergedListing.continuesOnPage = continuationListing.pageNumber;
    mergedListing.continuedFromPage = previousPageLastEntry.pageNumber;

    return mergedListing;
  }

  private findAndMergeRelatedEntries(
    listings: BusinessListing[],
    startIndex: number,
    mergedEntries: Set<number>
  ): BusinessListing {
    const baseListing = listings[startIndex];
    if (!baseListing) {
      throw new Error(`No listing found at index ${startIndex}`);
    }

    const relatedListings: BusinessListing[] = [baseListing];

    // Look for continuation entries
    for (let i = startIndex + 1; i < listings.length; i++) {
      if (mergedEntries.has(i)) continue;

      const listing = listings[i];
      if (!listing) continue;

      // Check if this listing continues the current entry
      if (this.isRelatedEntry(baseListing, listing)) {
        relatedListings.push(listing);
        mergedEntries.add(i);
      }
    }

    // Merge the related listings
    return this.mergeListings(relatedListings);
  }

  private isRelatedEntry(baseListing: BusinessListing, candidateListing: BusinessListing): boolean {
    // Only consider entries related if they have explicit continuation flags
    // This prevents incorrect merging of unrelated entries

    // Check if pages are consecutive
    if (Math.abs(candidateListing.pageNumber - baseListing.pageNumber) > 1) {
      return false;
    }

    // Only merge if there are explicit continuation flags indicating these entries are related
    return candidateListing.continuedFromPage === baseListing.pageNumber ||
      baseListing.continuesOnPage === candidateListing.pageNumber ||
      (!!baseListing.continuesOnNextPage && candidateListing.pageNumber === baseListing.pageNumber + 1) ||
      (!!candidateListing.isContinuationFromPreviousPage && candidateListing.pageNumber === baseListing.pageNumber + 1);
  }

  private mergeListings(listings: BusinessListing[]): BusinessListing {
    if (listings.length === 0) {
      throw new Error('Cannot merge empty listings array');
    }

    const firstListing = listings[0];
    if (!firstListing) {
      throw new Error('First listing is undefined');
    }

    if (listings.length === 1) return firstListing;

    const mergedListing: BusinessListing = {
      ...firstListing,
      data: { ...firstListing.data }
    };

    // Collect raw text from all listings for concatenation
    const rawTexts: string[] = [];
    if (firstListing.rawText) {
      rawTexts.push(firstListing.rawText);
    }

    // Merge data from all related listings
    for (let i = 1; i < listings.length; i++) {
      const listing = listings[i];
      if (!listing) continue;

      // Merge data fields, preferring non-empty values from the first listing
      // Only fill in missing fields, don't overwrite existing ones
      for (const [key, value] of Object.entries(listing.data)) {
        if (value && value !== '' && value !== null &&
          (!mergedListing.data[key] || mergedListing.data[key] === '' || mergedListing.data[key] === null)) {
          mergedListing.data[key] = value;
        }
      }

      // Collect raw text
      if (listing.rawText) {
        rawTexts.push(listing.rawText);
      }

      // Update confidence to average
      mergedListing.confidence = (mergedListing.confidence + listing.confidence) / 2;
    }

    // Concatenate raw text from all pages
    if (rawTexts.length > 0) {
      mergedListing.rawText = rawTexts.join(' ');
    }

    // Mark as spanning multiple pages
    mergedListing.spanMultiplePages = true;
    const lastListing = listings[listings.length - 1];
    if (lastListing) {
      mergedListing.continuesOnPage = lastListing.pageNumber;
    }

    // Set continuedFromPage if this is a continuation merge
    if (listings.length > 1 && listings[1]?.isContinuationFromPreviousPage) {
      mergedListing.continuedFromPage = firstListing.pageNumber;
    }

    return mergedListing;
  }

  private calculateStatistics(listings: BusinessListing[], extractionResults: ExtractionResult[]) {
    const totalPages = extractionResults.length;
    const totalListings = listings.length;

    const averageEntriesPerPage = totalListings / totalPages;

    const pagesWithMultipleEntries = extractionResults.filter(
      result => result.totalEntries > 1
    ).length;

    const entriesSpanningPages = listings.filter(
      listing => listing.spanMultiplePages
    ).length;

    // Calculate completeness score based on how many fields are filled
    const totalFields = listings.length * Object.keys(listings[0]?.data || {}).length;
    const filledFields = listings.reduce((count, listing) => {
      return count + Object.values(listing.data).filter(value =>
        value !== null && value !== undefined && value !== ''
      ).length;
    }, 0);

    const completenessScore = totalFields > 0 ? filledFields / totalFields : 0;

    return {
      averageEntriesPerPage,
      pagesWithMultipleEntries,
      entriesSpanningPages,
      completenessScore,
    };
  }

  private async saveCombinedResult(result: CombinedResult): Promise<void> {
    const processingConfig = this.config.getProcessingConfig();
    const combinedDir = join(processingConfig.outputDir, 'combined');
    await FileUtils.ensureDirectoryExists(combinedDir);

    const filename = FileUtils.generateTimestampedFilename('combined_data', 'json');
    const filePath = join(combinedDir, filename);

    await FileUtils.writeJsonFile(filePath, result);
    await logger.info(`Combined result saved to: ${filePath}`);

    // Also save as latest.json
    const latestPath = join(combinedDir, 'latest.json');
    await FileUtils.writeJsonFile(latestPath, result);
  }

  private async applyCategoriesFromMapping(listings: BusinessListing[]): Promise<BusinessListing[]> {
    try {
      // Load category mapping
      const categoryMapping = await this.loadCategoryMapping();

      if (Object.keys(categoryMapping).length === 0) {
        await logger.info('No category mapping found, listings will not have categories assigned');
        return listings;
      }

      // Apply categories to listings based on their page numbers
      const listingsWithCategories = listings.map(listing => {
        const category = this.findCategoryForPage(listing.pageNumber, categoryMapping);
        if (category) {
          return {
            ...listing,
            category: category.categoryName
          };
        }
        return listing;
      });

      const categorizedCount = listingsWithCategories.filter(l => l.category).length;
      await logger.info(`Applied categories to ${categorizedCount} out of ${listings.length} listings`);

      return listingsWithCategories;
    } catch (error) {
      await logger.warn('Failed to apply categories from mapping', error);
      return listings;
    }
  }

  private async loadCategoryMapping(): Promise<CategoryMapping> {
    try {
      const processingConfig = this.config.getProcessingConfig();
      const categoryPath = join(processingConfig.outputDir, 'category.json');

      if (await FileUtils.fileExists(categoryPath)) {
        return await FileUtils.readJsonFile<CategoryMapping>(categoryPath);
      }

      return {};
    } catch (error) {
      await logger.warn('Failed to load category mapping', error);
      return {};
    }
  }

  private findCategoryForPage(pageNumber: number, categoryMapping: CategoryMapping): { categoryName: string } | null {
    // Find the most recent category that applies to this page
    let applicableCategory: { categoryName: string } | null = null;
    let latestCategoryPage = 0;

    for (const [pageStr, categoryInfo] of Object.entries(categoryMapping)) {
      const categoryPage = parseInt(pageStr, 10);

      // Category applies if it's on or before the current page
      if (categoryPage <= pageNumber && categoryPage > latestCategoryPage) {
        applicableCategory = { categoryName: categoryInfo.categoryName };
        latestCategoryPage = categoryPage;
      }
    }

    return applicableCategory;
  }

  async exportToExcel(combinedResult?: CombinedResult): Promise<string> {
    try {
      let result = combinedResult;

      if (!result) {
        // Load latest combined result
        const processingConfig = this.config.getProcessingConfig();
        const latestPath = join(processingConfig.outputDir, 'combined', 'latest.json');

        if (await FileUtils.fileExists(latestPath)) {
          result = await FileUtils.readJsonFile<CombinedResult>(latestPath);
        } else {
          throw new Error('No combined result found. Please run combine first.');
        }
      }

      const excelConfig = this.config.getExcelConfig();
      const options: ExcelExportOptions = {
        filename: excelConfig.filename,
        sheetName: excelConfig.sheetName,
        includeMetadata: true,
        includeStatistics: true,
        autoFitColumns: true,
      };

      const filePath = await this.excelService.exportToExcel(result, options);
      return filePath;

    } catch (error) {
      await logger.error('Excel export failed', error);
      throw error;
    }
  }
}

// CLI function for direct usage
export async function runCombine(): Promise<void> {
  const combiner = new CombineUtility();

  try {
    await combiner.init();
    const result = await combiner.combine();

    console.log('\n📊 Combination Results:');
    console.log(`📄 Pages processed: ${result.totalPages}`);
    console.log(`📋 Total listings: ${result.totalListings}`);
    console.log(`📈 Average entries per page: ${result.statistics.averageEntriesPerPage.toFixed(1)}`);
    console.log(`🔗 Entries spanning pages: ${result.statistics.entriesSpanningPages}`);
    console.log(`✅ Completeness score: ${(result.statistics.completenessScore * 100).toFixed(1)}%`);

    // Export to Excel
    console.log('\n📊 Exporting to Excel...');
    const excelPath = await combiner.exportToExcel(result);
    console.log(`✅ Excel file created: ${excelPath}`);

  } catch (error) {
    console.error('❌ Combination failed:', error);
    Deno.exit(1);
  }
}
